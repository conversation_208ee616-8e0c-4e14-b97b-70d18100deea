local QBCore = exports['qb-core']:GetCoreObject()

local isEscorted = false
local isEscorting = false
local isKidnapped = false
local isHandcuffed = false
local KidnapperID = 0
local draggerId = 0
local KidnapTxt = false
local escapeInProgress = false

local loadedAnimDicts = {}

local function LoadAnimDict(dict)
    if loadedAnimDicts[dict] then
        return 
    end

    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end

    loadedAnimDicts[dict] = true
end

local function StopKidnapping()
    local ped = PlayerPedId()
    DetachEntity(ped, true, false)
    ClearPedTasksImmediately(ped)
    ClearPedSecondaryTask(ped)
    isEscorted = false
    isKidnapped = false
    isEscorting = false
    KidnapperID = 0
    draggerId = 0
    escapeInProgress = false

    if KidnapTxt then
        exports['qb-ui']:HideText()
        KidnapTxt = false
    end

    TriggerEvent('hospital:client:isEscorted', isEscorted)
    TriggerEvent('hospital:client:SetEscortingState', isEscorting)
    TriggerEvent('qb-kidnapping:client:SetKidnapping', isEscorting)
end

RegisterNetEvent('police:client:KidnapPlayer', function()

    if isEscorting then
        TriggerEvent('police:client:GetKidnappedDragger', draggerId)
        return
    end

    local player, distance = QBCore.Functions.GetClosestPlayer()
    if player ~= -1 and distance < Config.MaxDistance then
        local playerId = GetPlayerServerId(player)
        local targetPed = GetPlayerPed(player)

        if not IsPedInAnyVehicle(targetPed) then
            if not isHandcuffed and not isEscorted and not isEscorting then
                TriggerServerEvent("police:server:KidnapPlayer", playerId)
            else
                QBCore.Functions.Notify(Config.Lang.error.already_escorting, "error")
            end
        else
            QBCore.Functions.Notify(Config.Lang.error.in_vehicle, "error")
        end
    else
        QBCore.Functions.Notify(Config.Lang.error.none_nearby, "error")
    end
end)

RegisterNetEvent('police:client:PutPlayerInVehicle', function()
    if isEscorting and draggerId ~= 0 then
        TriggerServerEvent("police:server:PutPlayerInVehicle", draggerId)
    else
        QBCore.Functions.Notify(Config.Lang.error.not_escorting, "error")
    end
end)

RegisterNetEvent('police:client:SetPlayerOutVehicle', function()
    if draggerId ~= 0 then
        TriggerServerEvent("police:server:SetPlayerOutVehicle", draggerId)
    else
        QBCore.Functions.Notify(Config.Lang.error.not_escorting, "error")
    end
end)

RegisterNetEvent('police:client:GetKidnappedTarget', function(playerId)
    local ped = PlayerPedId()
    QBCore.Functions.GetPlayerData(function(PlayerData)
        if not isEscorted then
            isEscorted = true
            isKidnapped = true
            KidnapperID = playerId
            draggerId = playerId
            local dragger = GetPlayerPed(GetPlayerFromServerId(playerId))
            
            LoadAnimDict(Config.Animations.victim.dict)
            
            AttachEntityToEntity(ped, dragger, 0, 
                Config.AttachSettings.x, Config.AttachSettings.y, Config.AttachSettings.z,
                Config.AttachSettings.xRot, Config.AttachSettings.yRot, Config.AttachSettings.zRot,
                false, false, false, false, 2, false)
            TaskPlayAnim(ped, Config.Animations.victim.dict, Config.Animations.victim.anim, 
                8.0, -8.0, 100000, Config.Animations.victim.flag, 0, false, false, false)
        else
            StopKidnapping()
        end
        TriggerEvent('hospital:client:isEscorted', isEscorted)
    end)
end)

RegisterNetEvent('police:client:GetKidnappedDragger', function(playerId)
    QBCore.Functions.GetPlayerData(function(PlayerData)
        if not isEscorting then
            draggerId = playerId
            local dragger = PlayerPedId()

            ClearPedSecondaryTask(dragger)
            ClearPedTasksImmediately(dragger)

            CreateThread(function()
                Wait(500)

                LoadAnimDict(Config.Animations.kidnapper.dict)

                TaskPlayAnim(dragger, Config.Animations.kidnapper.dict, Config.Animations.kidnapper.anim,
                    8.0, -8.0, 100000, Config.Animations.kidnapper.flag, 0, false, false, false)
                isEscorting = true

                TriggerEvent('hospital:client:SetEscortingState', isEscorting)
                TriggerEvent('qb-kidnapping:client:SetKidnapping', isEscorting)


            end)
        else
            local dragger = PlayerPedId()
            ClearPedSecondaryTask(dragger)
            ClearPedTasksImmediately(dragger)
            isEscorting = false

            TriggerEvent('hospital:client:SetEscortingState', isEscorting)
            TriggerEvent('qb-kidnapping:client:SetKidnapping', isEscorting)

            if draggerId ~= 0 then
                TriggerServerEvent('police:server:RemoveKidnapPlayer', draggerId)
            end
        end
    end)
end)

RegisterNetEvent('police:client:RemoveKidnappedTarget', function()

    if isEscorted or isKidnapped then
        StopKidnapping()
    else

        if isEscorting then
            local ped = PlayerPedId()

            ClearPedSecondaryTask(ped)
            ClearPedTasksImmediately(ped)
            StopAnimTask(ped, Config.Animations.kidnapper.dict, Config.Animations.kidnapper.anim, 1.0)

            isEscorting = false
            draggerId = 0

            TriggerEvent('hospital:client:SetEscortingState', false)
            TriggerEvent('qb-kidnapping:client:SetKidnapping', false)
        end
    end
end)

RegisterNetEvent('police:client:CheckVehicleDistance', function(kidnapperId)
    if Config.Debug then
        print("[KIDNAP DEBUG] CheckVehicleDistance event received, kidnapperId:", kidnapperId)
    end

    if not isKidnapped then
        if Config.Debug then
            print("[KIDNAP DEBUG] Player is not kidnapped")
        end
        return
    end

    local vehicle = QBCore.Functions.GetClosestVehicle()
    if vehicle == 0 or not DoesEntityExist(vehicle) then
        TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, Config.Lang.error.no_vehicle_nearby, "error")
        return
    end

    local kidnapper = GetPlayerPed(GetPlayerFromServerId(kidnapperId))
    if not kidnapper or not DoesEntityExist(kidnapper) then
        TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, "Cannot locate kidnapper", "error")
        return
    end

    local vehicleCoords = GetEntityCoords(vehicle)
    local kidnapperCoords = GetEntityCoords(kidnapper)
    local distance = #(kidnapperCoords - vehicleCoords)

    if Config.Debug then
        print("[KIDNAP DEBUG] Distance between kidnapper and vehicle:", distance)
    end

    if distance <= Config.MaxDistance then
        TriggerServerEvent('police:server:ConfirmPutInVehicle', kidnapperId)
    else
        TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, Config.Lang.error.vehicle_too_far, "error")
    end
end)

RegisterNetEvent('police:client:CheckVehicleDistanceForExit', function(kidnapperId)
    if Config.Debug then
        print("[KIDNAP DEBUG] CheckVehicleDistanceForExit event received, kidnapperId:", kidnapperId)
    end

    if not isKidnapped then
        if Config.Debug then
            print("[KIDNAP DEBUG] Player is not kidnapped")
        end
        return
    end

    local ped = PlayerPedId()
    if not IsPedInAnyVehicle(ped) then
        TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, Config.Lang.error.not_in_vehicle, "error")
        return
    end

    local kidnapper = GetPlayerPed(GetPlayerFromServerId(kidnapperId))
    if not kidnapper or not DoesEntityExist(kidnapper) then
        TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, "Cannot locate kidnapper", "error")
        return
    end

    local vehicle = GetVehiclePedIsIn(ped, false)
    local vehicleCoords = GetEntityCoords(vehicle)
    local kidnapperCoords = GetEntityCoords(kidnapper)
    local distance = #(kidnapperCoords - vehicleCoords)

    if Config.Debug then
        print("[KIDNAP DEBUG] Distance between kidnapper and vehicle:", distance)
    end

    if distance <= Config.MaxDistance then
        TriggerServerEvent('police:server:ConfirmTakeOutVehicle', kidnapperId)
    else
        TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, Config.Lang.error.vehicle_too_far, "error")
    end
end)

RegisterNetEvent('police:client:PutInVehicle', function(kidnapperId)
    if Config.Debug then
        print("[KIDNAP DEBUG] PutInVehicle event received, isKidnapped:", isKidnapped, "kidnapperId:", kidnapperId)
    end

    if isKidnapped then
        local ped = PlayerPedId()
        local vehicle = QBCore.Functions.GetClosestVehicle()

        if vehicle ~= 0 and DoesEntityExist(vehicle) then
            local maxSeats = GetVehicleMaxNumberOfPassengers(vehicle)
            local freeSeat = nil

            for i = maxSeats - 1, 0, -1 do
                if IsVehicleSeatFree(vehicle, i) then
                    freeSeat = i
                    break
                end
            end

            if freeSeat then
                if Config.Debug then
                    print("[KIDNAP DEBUG] Detaching and clearing animations")
                end

                DetachEntity(ped, true, false)
                ClearPedTasksImmediately(ped)
                ClearPedSecondaryTask(ped)
                StopAnimTask(ped, Config.Animations.victim.dict, Config.Animations.victim.anim, 1.0)

                isEscorted = false 

                Wait(1000)

                ClearPedTasksImmediately(ped)

                if Config.Debug then
                    print("[KIDNAP DEBUG] Putting player in vehicle seat:", freeSeat)
                end

                SetPedIntoVehicle(ped, vehicle, freeSeat)

                isKidnapped = true 

                if KidnapTxt then
                    exports['qb-ui']:HideText()
                    KidnapTxt = false
                end

                TriggerEvent('hospital:client:isEscorted', false)

                if Config.Debug then
                    print("[KIDNAP DEBUG] Player put in vehicle successfully, isEscorted:", isEscorted)
                end

                if kidnapperId then
                    TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, Config.Lang.success.put_in_vehicle, "success")
                end
            else

                if kidnapperId then
                    TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, Config.Lang.error.no_free_seats, "error")
                end
            end
        else

            if kidnapperId then
                TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, Config.Lang.error.no_vehicle_nearby, "error")
            end
        end
    else
        if Config.Debug then
            print("[KIDNAP DEBUG] Player is not kidnapped")
        end
    end
end)

RegisterNetEvent('police:client:StopEscorting', function()
    if Config.Debug then
        print("[KIDNAP DEBUG] StopEscorting event received, isEscorting:", isEscorting)
    end

    if isEscorting then
        local ped = PlayerPedId()

        ClearPedSecondaryTask(ped)
        ClearPedTasksImmediately(ped)
        StopAnimTask(ped, Config.Animations.kidnapper.dict, Config.Animations.kidnapper.anim, 1.0)

        isEscorting = false
        draggerId = 0

        TriggerEvent('hospital:client:SetEscortingState', false)
        TriggerEvent('qb-kidnapping:client:SetKidnapping', false)

        if Config.Debug then
            print("[KIDNAP DEBUG] Kidnapper stopped escorting")
        end
    end
end)

RegisterNetEvent('police:client:SetOutVehicle', function(kidnapperId)
    if Config.Debug then
        print("[KIDNAP DEBUG] SetOutVehicle event received, isKidnapped:", isKidnapped, "kidnapperId:", kidnapperId)
    end

    if isKidnapped then
        local ped = PlayerPedId()

        if IsPedInAnyVehicle(ped) then

            local vehicle = GetVehiclePedIsIn(ped, false)

            TaskLeaveVehicle(ped, vehicle, 0)

            CreateThread(function()
                while IsPedInAnyVehicle(ped) do
                    Wait(100)
                end

                if Config.Debug then
                    print("[KIDNAP DEBUG] Player exited vehicle naturally")
                end
            end)

            ClearPedTasksImmediately(ped)

            isKidnapped = false
            isEscorted = false
            KidnapperID = 0
            draggerId = 0

            if KidnapTxt then
                exports['qb-ui']:HideText()
                KidnapTxt = false
            end

            TriggerEvent('hospital:client:isEscorted', false)

            if Config.Debug then
                print("[KIDNAP DEBUG] Player taken out of vehicle and is now free")
            end

            if kidnapperId then
                TriggerServerEvent('police:server:NotifyKidnapper', kidnapperId, Config.Lang.success.taken_out_vehicle, "success")
            end
        end
    end
end)

RegisterCommand('kidnap_escape', function()
    if isKidnapped and isEscorted and not escapeInProgress and not IsPedInAnyVehicle(PlayerPedId()) then
        escapeInProgress = true
        if KidnapperID ~= 0 then
            TriggerServerEvent('police:server:RemoveKidnapPlayer', KidnapperID)
        end
        if KidnapTxt then
            exports['qb-ui']:HideText()
            KidnapTxt = false
        end
    end
end, false)

RegisterKeyMapping('kidnap_escape', 'Escape from kidnapping', 'keyboard', 'E')

CreateThread(function()
    while true do
        local sleep = 1000

        if isKidnapped and isEscorted and not escapeInProgress then
            local ped = PlayerPedId() 
            local inVehicle = IsPedInAnyVehicle(ped)

            if not inVehicle then
                sleep = 500 

                if not KidnapTxt then
                    KidnapTxt = true
                    exports['qb-ui']:DrawText(Config.UI.escapeKey)
                end

                if not IsEntityPlayingAnim(ped, Config.Animations.victim.dict, Config.Animations.victim.anim, 3) then
                    LoadAnimDict(Config.Animations.victim.dict)
                    TaskPlayAnim(ped, Config.Animations.victim.dict, Config.Animations.victim.anim,
                        8.0, -8.0, 100000, Config.Animations.victim.flag, 0, false, false, false)

                    if Config.Debug then
                        print("[KIDNAP DEBUG] Restarted victim animation")
                    end
                end
            else

                if KidnapTxt then
                    exports['qb-ui']:HideText()
                    KidnapTxt = false
                end
            end
        elseif isEscorting then
            sleep = 500 
            local ped = PlayerPedId()

            if not IsEntityPlayingAnim(ped, Config.Animations.kidnapper.dict, Config.Animations.kidnapper.anim, 3) then
                LoadAnimDict(Config.Animations.kidnapper.dict)
                TaskPlayAnim(ped, Config.Animations.kidnapper.dict, Config.Animations.kidnapper.anim,
                    8.0, -8.0, 100000, Config.Animations.kidnapper.flag, 0, false, false, false)

                if Config.Debug then
                    print("[KIDNAP DEBUG] Restarted kidnapper animation")
                end
            end
        else

            if KidnapTxt then
                exports['qb-ui']:HideText()
                KidnapTxt = false
            end
        end

        Wait(sleep)
    end
end)



exports('IsKidnapped', function()
    return isKidnapped
end)

exports('IsEscorting', function()
    return isEscorting
end)

exports('GetKidnapperID', function()
    return KidnapperID
end)

exports('IsKidnappedInVehicle', function()
    return isKidnapped and IsPedInAnyVehicle(PlayerPedId())
end)
