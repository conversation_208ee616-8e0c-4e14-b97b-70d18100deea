local QBCore = exports['qb-core']:GetCoreObject()

local kidnapData = {}

local function IsPlayerKidnapped(playerId)
    return kidnapData[playerId] ~= nil
end

local function SetKidnapData(victimId, kidnapperId)
    kidnapData[victimId] = {
        kidnapper = kidnapperId,
        timestamp = os.time()
    }
end

local function RemoveKidnapData(victimId)
    kidnapData[victimId] = nil
end

RegisterNetEvent('police:server:KidnapPlayer', function(playerId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local EscortPlayer = QBCore.Functions.GetPlayer(playerId)
    
    if not Player or not EscortPlayer then
        return
    end
    
    if IsPlayerKidnapped(playerId) then

        if kidnapData[playerId] and kidnapData[playerId].kidnapper == src then

            RemoveKidnapData(playerId)
        else
            TriggerClientEvent('QBCore:Notify', src, Config.Lang.error.already_kidnapped, 'error')
            return
        end
    end
    
    SetKidnapData(playerId, src)
    
    TriggerClientEvent("police:client:GetKidnappedTarget", EscortPlayer.PlayerData.source, Player.PlayerData.source)
    TriggerClientEvent("police:client:GetKidnappedDragger", Player.PlayerData.source, EscortPlayer.PlayerData.source)
    
    if Config.Debug then
        print(string.format("[KIDNAP] Player %s (%d) kidnapped player %s (%d)", 
            Player.PlayerData.name, src, EscortPlayer.PlayerData.name, playerId))
    end
    
    TriggerClientEvent('QBCore:Notify', src, Config.Lang.success.kidnapped, 'success')
end)

RegisterNetEvent('police:server:RemoveKidnapPlayer', function(kidnapperId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local KidnapperPlayer = QBCore.Functions.GetPlayer(kidnapperId)

    if not Player then
        return
    end

    RemoveKidnapData(src)

    TriggerClientEvent("police:client:RemoveKidnappedTarget", src)
    if KidnapperPlayer then
        TriggerClientEvent("police:client:RemoveKidnappedTarget", KidnapperPlayer.PlayerData.source)
    end

    if Config.Debug then
        print(string.format("[KIDNAP] Player %s (%d) escaped from kidnapping",
            Player.PlayerData.name, src))
    end

    TriggerClientEvent('QBCore:Notify', src, Config.Lang.success.escaped, 'success')
end)

RegisterNetEvent('police:server:PutPlayerInVehicle', function(victimId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local VictimPlayer = QBCore.Functions.GetPlayer(victimId)

    if not Player or not VictimPlayer then
        return
    end

    if not IsPlayerKidnapped(victimId) or kidnapData[victimId].kidnapper ~= src then
        return
    end

    TriggerClientEvent("police:client:CheckVehicleDistance", VictimPlayer.PlayerData.source, src)

    if Config.Debug then
        print(string.format("[KIDNAP] Player %s (%d) attempting to put %s (%d) in vehicle",
            Player.PlayerData.name, src, VictimPlayer.PlayerData.name, victimId))
    end
end)

RegisterNetEvent('police:server:ConfirmPutInVehicle', function(kidnapperId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(kidnapperId)
    local VictimPlayer = QBCore.Functions.GetPlayer(src)

    if not Player or not VictimPlayer then
        return
    end

    if not IsPlayerKidnapped(src) or kidnapData[src].kidnapper ~= kidnapperId then
        return
    end

    TriggerClientEvent("police:client:PutInVehicle", src, kidnapperId)

    TriggerClientEvent("police:client:StopEscorting", kidnapperId)

    if Config.Debug then
        print(string.format("[KIDNAP] Player %s (%d) put %s (%d) in vehicle",
            Player.PlayerData.name, kidnapperId, VictimPlayer.PlayerData.name, src))
    end
end)

RegisterNetEvent('police:server:NotifyKidnapper', function(kidnapperId, message, type)
    local src = source
    local KidnapperPlayer = QBCore.Functions.GetPlayer(kidnapperId)

    if KidnapperPlayer then
        TriggerClientEvent('QBCore:Notify', kidnapperId, message, type)
    end
end)

RegisterNetEvent('police:server:SetPlayerOutVehicle', function(victimId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local VictimPlayer = QBCore.Functions.GetPlayer(victimId)

    if not Player or not VictimPlayer then
        return
    end

    if not IsPlayerKidnapped(victimId) or kidnapData[victimId].kidnapper ~= src then
        return
    end

    TriggerClientEvent("police:client:CheckVehicleDistanceForExit", VictimPlayer.PlayerData.source, src)

    if Config.Debug then
        print(string.format("[KIDNAP] Player %s (%d) attempting to take %s (%d) out of vehicle",
            Player.PlayerData.name, src, VictimPlayer.PlayerData.name, victimId))
    end
end)

RegisterNetEvent('police:server:ConfirmTakeOutVehicle', function(kidnapperId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(kidnapperId)
    local VictimPlayer = QBCore.Functions.GetPlayer(src)

    if not Player or not VictimPlayer then
        return
    end

    if not IsPlayerKidnapped(src) or kidnapData[src].kidnapper ~= kidnapperId then
        return
    end

    TriggerClientEvent("police:client:SetOutVehicle", src, kidnapperId)

    RemoveKidnapData(src)

    TriggerClientEvent("police:client:StopEscorting", kidnapperId)

    if Config.Debug then
        print(string.format("[KIDNAP] Player %s (%d) took %s (%d) out of vehicle and released them",
            Player.PlayerData.name, kidnapperId, VictimPlayer.PlayerData.name, src))
    end
end)

AddEventHandler('playerDropped', function(reason)
    local src = source
    
    if IsPlayerKidnapped(src) then
        local kidnapperData = kidnapData[src]
        if kidnapperData then
            local KidnapperPlayer = QBCore.Functions.GetPlayer(kidnapperData.kidnapper)
            if KidnapperPlayer then
                TriggerClientEvent("police:client:RemoveKidnappedTarget", KidnapperPlayer.PlayerData.source)
            end
        end
        RemoveKidnapData(src)
    end
    
    for victimId, data in pairs(kidnapData) do
        if data.kidnapper == src then
            local VictimPlayer = QBCore.Functions.GetPlayer(victimId)
            if VictimPlayer then
                TriggerClientEvent("police:client:RemoveKidnappedTarget", VictimPlayer.PlayerData.source)
            end
            RemoveKidnapData(victimId)
        end
    end
end)

exports('IsPlayerKidnapped', IsPlayerKidnapped)
exports('GetKidnapData', function()
    return kidnapData
end)

if Config.Debug then
    QBCore.Commands.Add('kidnap', 'Kidnap nearest player (DEBUG)', {}, false, function(source, args)
        TriggerClientEvent('police:client:KidnapPlayer', source)
    end)
    
    QBCore.Commands.Add('unkidnap', 'Release from kidnapping (DEBUG)', {}, false, function(source, args)
        TriggerServerEvent('police:server:RemoveKidnapPlayer', 0)
    end)
end
